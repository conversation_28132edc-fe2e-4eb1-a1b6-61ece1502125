import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { notificationApi, userApi, applicationApi } from '@/services/api';

const TestBackendConnections: React.FC = () => {
  const [results, setResults] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState<Record<string, boolean>>({});

  const testEndpoint = async (name: string, testFn: () => Promise<any>) => {
    setLoading(prev => ({ ...prev, [name]: true }));
    try {
      const result = await testFn();
      setResults(prev => ({ 
        ...prev, 
        [name]: { 
          success: true, 
          data: result,
          timestamp: new Date().toISOString()
        } 
      }));
    } catch (error) {
      setResults(prev => ({ 
        ...prev, 
        [name]: { 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        } 
      }));
    } finally {
      setLoading(prev => ({ ...prev, [name]: false }));
    }
  };

  const testNotifications = () => testEndpoint('notifications', async () => {
    const response = await notificationApi.getNotifications({ limit: 5 });
    return response;
  });

  const testUnreadCount = () => testEndpoint('unreadCount', async () => {
    const response = await notificationApi.getUnreadCount();
    return response;
  });

  const testUserProfile = () => testEndpoint('userProfile', async () => {
    const response = await userApi.getProfile();
    return response;
  });

  const testApplicationStats = () => testEndpoint('applicationStats', async () => {
    const response = await applicationApi.getStats();
    return response;
  });

  const testAll = () => {
    testNotifications();
    testUnreadCount();
    testUserProfile();
    testApplicationStats();
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Backend Connection Tests</h1>
        <Button onClick={testAll} disabled={Object.values(loading).some(Boolean)}>
          Test All Endpoints
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Notifications Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Notifications API
              <Button 
                size="sm" 
                onClick={testNotifications}
                disabled={loading.notifications}
              >
                {loading.notifications ? 'Testing...' : 'Test'}
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {results.notifications ? (
              <div className="space-y-2">
                <Badge variant={results.notifications.success ? 'default' : 'destructive'}>
                  {results.notifications.success ? 'Success' : 'Failed'}
                </Badge>
                <div className="text-sm">
                  <strong>Timestamp:</strong> {new Date(results.notifications.timestamp).toLocaleString()}
                </div>
                {results.notifications.success ? (
                  <div className="text-sm">
                    <strong>Data:</strong>
                    <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-32">
                      {JSON.stringify(results.notifications.data, null, 2)}
                    </pre>
                  </div>
                ) : (
                  <div className="text-sm text-red-600">
                    <strong>Error:</strong> {results.notifications.error}
                  </div>
                )}
              </div>
            ) : (
              <div className="text-gray-500">Not tested yet</div>
            )}
          </CardContent>
        </Card>

        {/* Unread Count Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Unread Count API
              <Button 
                size="sm" 
                onClick={testUnreadCount}
                disabled={loading.unreadCount}
              >
                {loading.unreadCount ? 'Testing...' : 'Test'}
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {results.unreadCount ? (
              <div className="space-y-2">
                <Badge variant={results.unreadCount.success ? 'default' : 'destructive'}>
                  {results.unreadCount.success ? 'Success' : 'Failed'}
                </Badge>
                <div className="text-sm">
                  <strong>Timestamp:</strong> {new Date(results.unreadCount.timestamp).toLocaleString()}
                </div>
                {results.unreadCount.success ? (
                  <div className="text-sm">
                    <strong>Data:</strong>
                    <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-32">
                      {JSON.stringify(results.unreadCount.data, null, 2)}
                    </pre>
                  </div>
                ) : (
                  <div className="text-sm text-red-600">
                    <strong>Error:</strong> {results.unreadCount.error}
                  </div>
                )}
              </div>
            ) : (
              <div className="text-gray-500">Not tested yet</div>
            )}
          </CardContent>
        </Card>

        {/* User Profile Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              User Profile API
              <Button 
                size="sm" 
                onClick={testUserProfile}
                disabled={loading.userProfile}
              >
                {loading.userProfile ? 'Testing...' : 'Test'}
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {results.userProfile ? (
              <div className="space-y-2">
                <Badge variant={results.userProfile.success ? 'default' : 'destructive'}>
                  {results.userProfile.success ? 'Success' : 'Failed'}
                </Badge>
                <div className="text-sm">
                  <strong>Timestamp:</strong> {new Date(results.userProfile.timestamp).toLocaleString()}
                </div>
                {results.userProfile.success ? (
                  <div className="text-sm">
                    <strong>Data:</strong>
                    <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-32">
                      {JSON.stringify(results.userProfile.data, null, 2)}
                    </pre>
                  </div>
                ) : (
                  <div className="text-sm text-red-600">
                    <strong>Error:</strong> {results.userProfile.error}
                  </div>
                )}
              </div>
            ) : (
              <div className="text-gray-500">Not tested yet</div>
            )}
          </CardContent>
        </Card>

        {/* Application Stats Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Application Stats API
              <Button 
                size="sm" 
                onClick={testApplicationStats}
                disabled={loading.applicationStats}
              >
                {loading.applicationStats ? 'Testing...' : 'Test'}
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {results.applicationStats ? (
              <div className="space-y-2">
                <Badge variant={results.applicationStats.success ? 'default' : 'destructive'}>
                  {results.applicationStats.success ? 'Success' : 'Failed'}
                </Badge>
                <div className="text-sm">
                  <strong>Timestamp:</strong> {new Date(results.applicationStats.timestamp).toLocaleString()}
                </div>
                {results.applicationStats.success ? (
                  <div className="text-sm">
                    <strong>Data:</strong>
                    <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-32">
                      {JSON.stringify(results.applicationStats.data, null, 2)}
                    </pre>
                  </div>
                ) : (
                  <div className="text-sm text-red-600">
                    <strong>Error:</strong> {results.applicationStats.error}
                  </div>
                )}
              </div>
            ) : (
              <div className="text-gray-500">Not tested yet</div>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm space-y-2">
            <p><strong>Total Tests:</strong> {Object.keys(results).length}</p>
            <p><strong>Successful:</strong> {Object.values(results).filter((r: any) => r.success).length}</p>
            <p><strong>Failed:</strong> {Object.values(results).filter((r: any) => !r.success).length}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TestBackendConnections;
