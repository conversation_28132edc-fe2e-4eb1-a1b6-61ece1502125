Write-Host "Starting TalentSol ATS on port 8080..." -ForegroundColor Green

# Function to kill processes using port 8080
function Stop-ProcessOnPort {
    param([int]$Port)

    Write-Host "Checking for processes using port $Port..." -ForegroundColor Yellow

    try {
        # Get processes using the port
        $connections = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue

        if ($connections) {
            Write-Host "Found processes using port $Port. Stopping them..." -ForegroundColor Yellow

            foreach ($conn in $connections) {
                $processId = $conn.OwningProcess
                if ($processId -gt 0) {
                    try {
                        $process = Get-Process -Id $processId -ErrorAction SilentlyContinue
                        if ($process) {
                            Write-Host "Stopping process: $($process.ProcessName) (PID: $processId)" -ForegroundColor Red
                            Stop-Process -Id $processId -Force -ErrorAction SilentlyContinue
                        }
                    } catch {
                        Write-Host "Could not stop process $processId" -ForegroundColor Yellow
                    }
                }
            }

            Write-Host "Waiting for port to be freed..." -ForegroundColor Yellow
            Start-Sleep -Seconds 3
        } else {
            Write-Host "Port $Port is available" -ForegroundColor Green
        }
    } catch {
        Write-Host "Could not check port usage: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# Stop processes using port 8080 (try multiple times)
for ($i = 1; $i -le 3; $i++) {
    Write-Host "Attempt $i to free port 8080..." -ForegroundColor Cyan
    Stop-ProcessOnPort -Port 8080

    # Check if port is free
    $portCheck = Get-NetTCPConnection -LocalPort 8080 -ErrorAction SilentlyContinue
    if (-not $portCheck) {
        Write-Host "Port 8080 is now available!" -ForegroundColor Green
        break
    } else {
        Write-Host "Port still in use, trying more aggressive approach..." -ForegroundColor Yellow

        # Try to kill all httpd and node processes
        Get-Process -Name "httpd" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
        Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue

        Start-Sleep -Seconds 2
    }
}

# Final check
$portCheck = Get-NetTCPConnection -LocalPort 8080 -ErrorAction SilentlyContinue
if ($portCheck) {
    Write-Host "Port 8080 is still in use after multiple attempts." -ForegroundColor Red
    Write-Host "Manual intervention required. Try:" -ForegroundColor Yellow
    Write-Host "1. Close any web browsers or database tools" -ForegroundColor White
    Write-Host "2. Stop Apache/httpd service if running" -ForegroundColor White
    Write-Host "3. Run: taskkill /f /im httpd.exe" -ForegroundColor White
    Write-Host "4. Run: taskkill /f /im node.exe" -ForegroundColor White
    exit 1
} else {
    Write-Host "Success! Port 8080 is now available!" -ForegroundColor Green
}

# Start TalentSol ATS
Write-Host "Starting TalentSol ATS..." -ForegroundColor Green
npm run dev
