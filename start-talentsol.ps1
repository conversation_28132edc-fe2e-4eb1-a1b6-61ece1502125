Write-Host "🚀 Starting TalentSol ATS on port 8080..." -ForegroundColor Green

# Function to kill processes using port 8080
function Stop-ProcessOnPort {
    param([int]$Port)
    
    Write-Host "🔍 Checking for processes using port $Port..." -ForegroundColor Yellow
    
    try {
        # Get processes using the port
        $connections = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
        
        if ($connections) {
            Write-Host "📋 Found processes using port $Port. Stopping them..." -ForegroundColor Yellow
            
            foreach ($conn in $connections) {
                $processId = $conn.OwningProcess
                if ($processId -gt 0) {
                    try {
                        $process = Get-Process -Id $processId -ErrorAction SilentlyContinue
                        if ($process) {
                            Write-Host "🛑 Stopping process: $($process.ProcessName) (PID: $processId)" -ForegroundColor Red
                            Stop-Process -Id $processId -Force -ErrorAction SilentlyContinue
                        }
                    } catch {
                        Write-Host "⚠️  Could not stop process $processId" -ForegroundColor Yellow
                    }
                }
            }
            
            Write-Host "⏳ Waiting for port to be freed..." -ForegroundColor Yellow
            Start-Sleep -Seconds 3
        } else {
            Write-Host "✅ Port $Port is available" -ForegroundColor Green
        }
    } catch {
        Write-Host "⚠️  Could not check port usage: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# Stop processes using port 8080
Stop-ProcessOnPort -Port 8080

# Verify port is free
Write-Host "🔍 Verifying port 8080 is available..." -ForegroundColor Yellow
$portCheck = Get-NetTCPConnection -LocalPort 8080 -ErrorAction SilentlyContinue
if ($portCheck) {
    Write-Host "❌ Port 8080 is still in use. You may need to manually stop the conflicting application." -ForegroundColor Red
    Write-Host "💡 Try closing any web browsers, database tools, or other applications that might be using port 8080." -ForegroundColor Cyan
    exit 1
} else {
    Write-Host "✅ Port 8080 is now available!" -ForegroundColor Green
}

# Start TalentSol ATS
Write-Host "🚀 Starting TalentSol ATS..." -ForegroundColor Green
npm run dev
